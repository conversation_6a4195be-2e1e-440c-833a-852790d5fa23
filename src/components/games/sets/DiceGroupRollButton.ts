import { ATTRIBUTE_TRANSLATION_KEY, TRANSLATIONS } from "TRANSLATIONS";
import {
  DiceRollOptions,
  ResultComponents,
  ResultDescription,
  ResultPic,
  ResultValue,
} from "types/results.ts";
import { animateCSS } from "utils/animateCss.ts";
import { devLog } from "utils/devLog.ts";
import { safeCustomDefine } from "utils/safeCustomDefine.ts";
import { SwitchHideResult } from "utils/SwitchHideResult.ts";
import { MessagesSection } from "../../sidebar/MessagesSection.ts";
import { Die } from "../playables/dice/Die.ts";
import { DieClassName } from "../playables/dice/helpers/diceClassesIndex.ts";
import { GenericComponent } from "../../core/GenericComponent.ts";
import { TranslationController } from "controllers/TranslationController.ts";
import { Videocall } from "../../room/Videocall.ts";
import { MessageDetails } from "types/communication.ts";
import { Notify } from "notiflix";

type DiceRollAllData = {
  dieName: string;
  size: number;
  result: ResultComponents;
  dieRollOptions: DiceRollOptions;
  overlayAnimations: string[];
  throwAudios: string[];
  resultAudios: string[];
};

export class DiceGroupRollButton extends GenericComponent {
  connectedCallback() {
    this.classList.add("m-auto");
    const buttonText = this.getAttribute("button-text") || "group roll";
    this.innerHTML = `
            <button class="border transform hover:scale-110 hover:text-white hover:bg-blue-600 px-4 rounded-lg">
                <span 
                    ${ATTRIBUTE_TRANSLATION_KEY}="${buttonText}" 
                    class="uppercase whitespace-nowrap"
                ></span>
            </button>
        `;
    const button = this.querySelector("button") as HTMLButtonElement;
    button.addEventListener("click", () => {
      this.roll();
    });
  }
  getAssociatedDice(): Die[] | false {
    const id = this.getAttribute("id");
    if (!id) {
      devLog({
        message: "DiceGroupRollButton.getAssociatedDice: id is not defined",
        element: this,
      });
      return false;
    }
    const associatedDice: Die[] = Array.from(
      document.querySelectorAll(`[${DiceGroupRollButton.tag}="${id}"]`),
    ) as Die[];
    const associatedButNotDice = associatedDice.filter((die) =>
      !(die instanceof Die)
    );
    if (associatedButNotDice.length > 0) {
      devLog({
        message: "DiceGroupRollButton associated elements are not dice",
        associatedButNotDice,
      });
    }
    if (associatedButNotDice.length === associatedDice.length) {
      devLog({
        message: "DiceGroupRollButton has no associated dice",
        associatedDice,
      });
      return false;
    }
    return associatedDice;
  }
  getChosenDice(): Die[] | false {
    const associatedDice: Die[] | false = this.getAssociatedDice();
    if (!associatedDice || associatedDice.length < 1) {
      return false;
    }
    const unrollableDice: Die[] = associatedDice.filter((die) =>
      !die.isRollable
    );
    if (unrollableDice.length > 0) {
      return false;
    }
    return associatedDice.filter((die) => {
      const isOptional = die.hasAttribute(
        `${DiceGroupRollButton.tag}-optional`,
      );
      if (isOptional) {
        return die.dieGroupCheckbox?.checked;
      }
      return die.rolls > 0;
    });
  }
  async roll() {
    const chosenDice: Die[] | false = this.getChosenDice();
    if (!chosenDice) {
      return;
    }
    if (chosenDice.length < 1) {
      const translationController = await TranslationController.instance;
      const warning = TRANSLATIONS.atLeastOneDie.values
        .text[await translationController.language];
      Notify.warning(warning);

      const associatedDie = this.getAssociatedDice() || [];
      associatedDie.forEach((die) => {
        const dieGroupCheckbox = die.dieGroupCheckbox;
        if (dieGroupCheckbox) {
          animateCSS(dieGroupCheckbox, "heartBeat");
        }
      });
      return;
    }
    const diceResultPromises = chosenDice.map(async (die: Die) => {
      return new Promise<DiceRollAllData | false>((resolve) => {
        die.generateResult().then((result) => {
          if (result.values.length < 1) {
            resolve(false);
            return;
          }
          die.querySelector("button")?.classList.add(
            "animate__animated",
            ...die.containerAnimations,
          );
          die.querySelector("svg")?.classList.add(
            "animate__animated",
            ...die.dieAnimations,
          );
          const overlayAnimations = die.overlayAnimations || [];
          const throwAudios = die.throwAudios || [];
          const resultAudios = die.resultAudios || [];
          const dieRollOptions: DiceRollOptions = {
            die: die.instanceClassName as DieClassName,
            dieName: die.dieName,
            result,
          };
          const diceRollAllData: DiceRollAllData = {
            dieName: die.dieName,
            size: die.dieSize,
            result: {
              values: result.values,
              pics: result.pics,
              description: result.description,
            },
            dieRollOptions,
            overlayAnimations,
            throwAudios,
            resultAudios,
          };
          return resolve(diceRollAllData);
        });
      });
    });
    const all: DiceRollAllData[] = (await Promise.all(diceResultPromises))
      .filter(Boolean) as DiceRollAllData[];
    const aggregatedResult: ResultComponents & {
      diceNames: string[];
      diceSizes: number[];
    } = all.reduce((aggregatedResult, diceRollAllData) => {
      const { result } = diceRollAllData;
      aggregatedResult.values = aggregatedResult.values.concat(result.values);
      aggregatedResult.pics = aggregatedResult.pics.concat(result.pics);
      aggregatedResult.description += ` ${result.description}`;
      const oneDieNamePerValue = Array.from(
        { length: result.values.length },
        (_) => diceRollAllData.dieName,
      );
      aggregatedResult.diceNames.push(...oneDieNamePerValue);
      const oneDieSizePerValue = Array.from(
        { length: result.values.length },
        (_) => diceRollAllData.size,
      );
      aggregatedResult.diceSizes.push(...oneDieSizePerValue);
      return aggregatedResult;
    }, {
      values: [] as ResultValue[],
      pics: [] as ResultPic[],
      description: "" as ResultDescription,
      diceNames: [] as string[],
      diceSizes: [] as number[],
    });
    const resultConsolidationStrategy = this.getAttribute(
      "result-consolidation",
    )!;
    const shouldConsolidateResults: boolean = ["name", "size"]
      .includes(resultConsolidationStrategy);
    if (shouldConsolidateResults) {
      const diceIdentifiers = resultConsolidationStrategy === "name"
        ? aggregatedResult.diceNames
        : aggregatedResult.diceSizes.map((size) => `d${size}`);
      const uniqueIdentifiers = diceIdentifiers.filter((
        value: string,
        index: number,
        self: string[],
      ) => self.indexOf(value) === index);
      const diceFormula: string = uniqueIdentifiers.reduce(
        (acc, identifier) => {
          const occurrencesInArray = diceIdentifiers.filter((name) =>
            name === identifier
          ).length;
          const formula = `${occurrencesInArray}${identifier}`;
          return `${acc} ${formula}`;
        },
        "",
      );
      aggregatedResult.description = `${diceFormula}`;
      const resultConsolidationSum = this.getAttribute(
        "result-consolidation-sum",
      )!;
      if (resultConsolidationSum === "true") {
        const sum = aggregatedResult.values.reduce((acc, curr) => {
          return (Number(acc) + Number(curr)).toString();
        });
        aggregatedResult.description += ` (${sum})`;
      }
    }
    const resultSorting = this.getAttribute("result-sorting");
    const shouldSortResults: boolean = resultSorting === "ascending" ||
      resultSorting === "descending";
    if (shouldSortResults) {
      const indexValuesSorted = aggregatedResult.values.map((value, index) => {
        return {
          index,
          value,
        };
      }).sort((a, b) => {
        if (resultSorting === "ascending") {
          return a.value > b.value ? 1 : -1;
        }
        return a.value < b.value ? 1 : -1;
      }).map(({ index }) => index);
      const sortedValues = indexValuesSorted.map((index) =>
        aggregatedResult.values[index]
      );
      const sortedPics = indexValuesSorted.map((index) =>
        aggregatedResult.pics[index]
      );
      aggregatedResult.values = sortedValues;
      aggregatedResult.pics = sortedPics;
    }
    const messagesSection = document.querySelector(
      MessagesSection.tag,
    ) as MessagesSection;
    const resultText: string = MessagesSection.formatDiceRoll(aggregatedResult);
    const timestamp: number = Date.now();
    const hidingResults: boolean = SwitchHideResult.active;
    const objectMessage: MessageDetails = {
      from: "You",
      timestamp,
      text: resultText +
        (hidingResults
          ? `<span ${ATTRIBUTE_TRANSLATION_KEY}="hidden to others"><span>`
          : ""),
      type: "die",
    };
    messagesSection.add(objectMessage);
    const resultValues = Object.values(all);
    const audioOptions = {
      throwAudios: resultValues.flatMap(({ throwAudios }) => throwAudios),
      resultAudios: resultValues.flatMap(({ resultAudios }) => resultAudios),
      values: aggregatedResult.values,
    };
    await Die.playAudioThrow(audioOptions);
    const messageWithAudio: MessageDetails = {
      ...objectMessage,
      audio: audioOptions,
    };
    await Videocall.getInstance<Videocall>().then(async (videocall) =>
      await videocall.sendMessage(messageWithAudio)
    );
  }
}
safeCustomDefine(DiceGroupRollButton);
