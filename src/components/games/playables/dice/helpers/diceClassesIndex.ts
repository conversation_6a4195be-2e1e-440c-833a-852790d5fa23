import { Deck } from "../../deck/Deck.ts";
import { AchtungCthulhuChallengeDie } from "../die/AchtungCthulhuChallengeDie.ts";
import { AchtungCthulhuTestDie } from "../die/AchtungCthulhuTestDie.ts";
import { AgonDie } from "../die/AgonDie.ts";
import { AvatarDie } from "../die/AvatarDie.ts";
import { BrokenCompassDie } from "../die/BrokenCompassDie.ts";
import { BrokenCompassLuckCoin } from "../die/BrokenCompassLuckCoin.ts";
import { CityOfMistDie } from "../die/CityOfMistDie.ts";
import { Coin } from "../die/Coin.ts";
import { CthulhuDarkDie } from "../die/CthulhuDarkDie.ts";
import { CustomDie } from "../die/CustomDie.ts";
import { FantasyWorldDie } from "../die/FantasyWorldDie.ts";
import { FudgeDie } from "../die/FudgeDie.ts";
import { LFRDie } from "../die/LFRDie.ts";
import { PolyDie } from "../die/PolyhedricDie.ts";
import { SeventhSeaDie } from "../die/SeventhSeaDie.ts";
import { SixBulletsDie } from "../die/SixBulletsDie.ts";
import { SpireDie } from "../die/SpireDie.ts";
import { ValravenDie } from "../die/ValravenDie.ts";
import { VampireDie } from "../die/VampireDie.ts";
import { VampireNumberDie } from "../die/VampireNumberDie.ts";
import { VampireV20Die } from "../die/VampireV20Die.ts";

export const diceClassesIndex = {
  AchtungCthulhuChallengeDie,
  AchtungCthulhuTestDie,
  AgonDie,
  AvatarDie,
  BrokenCompassDie,
  BrokenCompassLuckCoin,
  CityOfMistDie,
  Coin,
  CthulhuDarkDie,
  CustomDie,
  Deck,
  FantasyWorldDie,
  FudgeDie,
  LFRDie,
  PolyDie,
  SeventhSeaDie,
  SixBulletsDie,
  SpireDie,
  ValravenDie,
  VampireDie,
  VampireNumberDie,
  VampireV20Die,
};
export type DieClassName = keyof typeof diceClassesIndex;
export type DieClass = typeof diceClassesIndex[DieClassName];
